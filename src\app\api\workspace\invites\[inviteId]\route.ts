import 'server-only';

import { Permission } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/invites/{inviteId}:
 *   delete:
 *     summary: Cancel/revoke workspace invitation
 *     description: Cancel or revoke a workspace invitation. Only the inviter or users with INVITE_MEMBER permission can cancel invitations.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: inviteId
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation ID
 *     responses:
 *       200:
 *         description: Invitation cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invitation cancelled successfully"
 *       400:
 *         description: Bad request - invitation not found or cannot be cancelled
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot cancel this invitation
 *       500:
 *         description: Internal server error
 */
export async function DELETE(request: NextRequest, props: { params: Promise<{ inviteId: string }> }) {
  const params = await props.params;
  try {
    const { inviteId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get the invitation
    const invitation = await db.workspaceInvite.findUnique({
      where: {
        id: inviteId,
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 400 });
    }

    // Check if invitation can be cancelled (only pending invitations)
    if (invitation.status !== 'PENDING') {
      return NextResponse.json({ error: 'Only pending invitations can be cancelled' }, { status: 400 });
    }

    // Check if the current user can cancel this invitation
    // They can cancel if:
    // 1. They are the one who sent the invitation, OR
    // 2. They have INVITE_MEMBER permission in the workspace
    let canCancel = false;

    if (invitation.invitedBy === currentUser.uid) {
      canCancel = true;
    } else {
      // Check if user has INVITE_MEMBER permission in the workspace
      try {
        const membership = await db.workspaceMembership.findFirst({
          where: {
            userId: currentUser.uid,
            workspaceId: invitation.workspaceId!,
            role: {
              permissions: {
                some: {
                  id: Permission.INVITE_MEMBER,
                },
              },
            },
          },
        });
        canCancel = !!membership;
      } catch (error) {
        console.error('Error checking permissions:', error);
        canCancel = false;
      }
    }

    if (!canCancel) {
      return NextResponse.json({ error: 'You do not have permission to cancel this invitation' }, { status: 403 });
    }

    // Delete the invitation
    await db.workspaceInvite.delete({
      where: { id: inviteId },
    });

    return NextResponse.json({
      message: 'Invitation cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling workspace invitation:', error);
    return NextResponse.json({ error: 'Failed to cancel invitation' }, { status: 500 });
  }
}
