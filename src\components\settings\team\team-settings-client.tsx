'use client';

import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Chip from '@mui/material/Chip';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import { PlusIcon } from '@phosphor-icons/react/dist/ssr/Plus';
import { TrashIcon } from '@phosphor-icons/react/dist/ssr/Trash';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { useWorkspace } from '@/contexts/workspace-context';
import { useApiServices } from '@/hooks/use-api-services';
import { Role, User, WorkspaceInvite, WorkspaceMembership } from '@prisma/client';

type WorkspaceMemberWithDetails = WorkspaceMembership & {
  user: User;
  role: Role;
};

type WorkspaceInviteWithDetails = WorkspaceInvite & {
  invitedByUser: User;
  user: User | null;
};

export function TeamSettingsClient(): React.JSX.Element {
  const t = useTranslations('settings.team');
  const { currentWorkspace } = useWorkspace();
  const { workspaceApiService } = useApiServices();

  // State for members and invitations
  const [members, setMembers] = React.useState<WorkspaceMemberWithDetails[]>([]);
  const [invitations, setInvitations] = React.useState<WorkspaceInviteWithDetails[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // Load members and invitations
  const loadData = React.useCallback(async () => {
    if (!currentWorkspace) return;

    try {
      setLoading(true);
      setError(null);

      const [membersData, invitationsData] = await Promise.all([
        workspaceApiService.getWorkspaceMembers(currentWorkspace.id),
        workspaceApiService.getWorkspaceInvitations(currentWorkspace.id),
      ]);

      setMembers(membersData);
      setInvitations(invitationsData.filter((inv) => inv.status === 'PENDING'));
    } catch (err) {
      console.error('Error loading team data:', err);
      setError('Failed to load team data');
    } finally {
      setLoading(false);
    }
  }, [currentWorkspace, workspaceApiService]);

  // Load data on mount and when workspace changes
  React.useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle invitation cancellation
  const handleCancelInvitation = async (inviteId: string) => {
    try {
      await workspaceApiService.cancelInvitation(inviteId);
      // Reload data to reflect changes
      await loadData();
    } catch (err) {
      console.error('Error cancelling invitation:', err);
      setError('Failed to cancel invitation');
    }
  };

  // Handle invite member (placeholder for now)
  const handleInviteMember = () => {
    // TODO: Open invite member dialog
    console.log('Invite member clicked');
  };

  if (!currentWorkspace) {
    return (
      <Stack spacing={3}>
        <Typography variant='h6'>{t('title')}</Typography>
        <Typography color='text.secondary'>No workspace selected</Typography>
      </Stack>
    );
  }

  if (loading) {
    return (
      <Stack spacing={3}>
        <Stack direction='row' spacing={3} alignItems='center' justifyContent='space-between'>
          <Typography variant='h6'>{t('title')}</Typography>
          <Button variant='contained' startIcon={<PlusIcon fontSize='var(--icon-fontSize-md)' />} disabled>
            {t('inviteMember')}
          </Button>
        </Stack>
        <Typography>Loading...</Typography>
      </Stack>
    );
  }

  if (error) {
    return (
      <Stack spacing={3}>
        <Stack direction='row' spacing={3} alignItems='center' justifyContent='space-between'>
          <Typography variant='h6'>{t('title')}</Typography>
          <Button variant='contained' startIcon={<PlusIcon fontSize='var(--icon-fontSize-md)' />} disabled>
            {t('inviteMember')}
          </Button>
        </Stack>
        <Typography color='error'>{error}</Typography>
        <Button onClick={loadData}>Retry</Button>
      </Stack>
    );
  }

  return (
    <Stack spacing={3}>
      <Stack direction='row' spacing={3} alignItems='center' justifyContent='space-between'>
        <Typography variant='h6'>{t('title')}</Typography>
        <Button
          variant='contained'
          startIcon={<PlusIcon fontSize='var(--icon-fontSize-md)' />}
          onClick={handleInviteMember}
        >
          {t('inviteMember')}
        </Button>
      </Stack>

      {/* Current Members */}
      <Card>
        <CardHeader title={t('members')} />
        <Divider />
        <CardContent>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('name')}</TableCell>
                <TableCell>{t('email')}</TableCell>
                <TableCell>{t('role')}</TableCell>
                <TableCell align='right'>{t('actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {members.map((member) => (
                <TableRow key={member.userId}>
                  <TableCell>
                    <Stack direction='row' spacing={2} alignItems='center'>
                      <Avatar src={member.user.avatar || undefined}>
                        {member.user.displayName?.charAt(0) || member.user.email?.charAt(0) || 'U'}
                      </Avatar>
                      <Typography variant='body1'>
                        {member.user.displayName || member.user.email || 'Unknown User'}
                      </Typography>
                    </Stack>
                  </TableCell>
                  <TableCell>{member.user.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={member.role.name || member.role.id}
                      size='small'
                      color={member.role.id === 'owner' ? 'primary' : 'default'}
                    />
                  </TableCell>
                  <TableCell align='right'>
                    <Button variant='text' color='primary' size='small'>
                      {t('edit')}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pending Invitations */}
      {invitations.length > 0 && (
        <Card>
          <CardHeader title={t('pendingInvitations')} />
          <Divider />
          <CardContent>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('email')}</TableCell>
                  <TableCell>Invited By</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell align='right'>{t('actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invitations.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell>{invitation.email}</TableCell>
                    <TableCell>
                      {invitation.invitedByUser?.displayName || invitation.invitedByUser?.email || 'Unknown'}
                    </TableCell>
                    <TableCell>{new Date(invitation.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell align='right'>
                      <IconButton color='error' size='small' onClick={() => handleCancelInvitation(invitation.id)}>
                        <TrashIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {invitations.length === 0 && (
        <Card>
          <CardHeader title={t('pendingInvitations')} />
          <Divider />
          <CardContent>
            <Typography color='text.secondary' align='center'>
              {t('noPendingInvitations')}
            </Typography>
          </CardContent>
        </Card>
      )}
    </Stack>
  );
}
