/**
 * @openapi
 * components:
 *   schemas:
 *     Role:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         workspaceId:
 *           type: string
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         memberships:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceMembership'
 *         permissions:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Permissions'
 *         invites:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceInvite'
 *       required:
 *         - id
 *         - createdAt
 *         - updatedAt
 *         - memberships
 *         - permissions
 *         - invites
 *     Permissions:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         roles:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Role'
 *       required:
 *         - id
 *         - roles
 *     GitRepository:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         idOnChannel:
 *           type: string
 *           description: The id of this repository in the integration channel
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         workspaceId:
 *           type: string
 *         integration:
 *           type: object
 *           $ref: '#/components/schemas/WorkspaceIntegration'
 *         integrationId:
 *           type: string
 *         channel:
 *           type: string
 *           $ref: '#/components/schemas/IntegrationChannel'
 *         name:
 *           type: string
 *         defaultBranch:
 *           type: string
 *         visibility:
 *           type: string
 *           $ref: '#/components/schemas/GitRepositoryVisibility'
 *         state:
 *           type: string
 *           $ref: '#/components/schemas/GitRepositoryState'
 *         createdAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 *         archivedAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         createdBy:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         createdById:
 *           type: string
 *         deletedBy:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         deletedById:
 *           type: string
 *         archivedBy:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         archivedById:
 *           type: string
 *         commits:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/GitCommit'
 *         pullRequests:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/GitPullRequest'
 *         tasks:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Task'
 *       required:
 *         - id
 *         - idOnChannel
 *         - workspace
 *         - workspaceId
 *         - channel
 *         - name
 *         - defaultBranch
 *         - visibility
 *         - state
 *         - createdAt
 *         - updatedAt
 *         - commits
 *         - pullRequests
 *         - tasks
 *     GitCommit:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         sha:
 *           type: string
 *         repositoryId:
 *           type: string
 *         repository:
 *           type: object
 *           $ref: '#/components/schemas/GitRepository'
 *         branch:
 *           type: string
 *         url:
 *           type: string
 *         committedAt:
 *           type: string
 *           format: date-time
 *         authorId:
 *           type: string
 *         author:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         commiterId:
 *           type: string
 *         commiter:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         pusherId:
 *           type: string
 *         pusher:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         message:
 *           type: string
 *         modifiedFiles:
 *           type: string
 *         addedFiles:
 *           type: string
 *         deletedFiles:
 *           type: string
 *       required:
 *         - id
 *         - sha
 *         - repositoryId
 *         - repository
 *         - branch
 *         - url
 *         - committedAt
 *         - message
 *         - modifiedFiles
 *         - addedFiles
 *         - deletedFiles
 *     GitPullRequest:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         idOnChannel:
 *           type: string
 *           description: The id of this pull request in the integration channel
 *         repositoryId:
 *           type: string
 *         repository:
 *           type: object
 *           $ref: '#/components/schemas/GitRepository'
 *         author:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         authorId:
 *           type: string
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         state:
 *           type: string
 *           $ref: '#/components/schemas/GitPullRequestState'
 *         url:
 *           type: string
 *         number:
 *           type: integer
 *           description: The pull request number if exists
 *         createdAt:
 *           type: string
 *           format: date-time
 *         mergedAt:
 *           type: string
 *           format: date-time
 *         closedAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         closedById:
 *           type: string
 *         closedBy:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         isDraft:
 *           type: boolean
 *         addedLines:
 *           type: integer
 *         deletedLines:
 *           type: integer
 *         baseRef:
 *           type: string
 *         headRef:
 *           type: string
 *         baseSha:
 *           type: string
 *         headSha:
 *           type: string
 *       required:
 *         - id
 *         - idOnChannel
 *         - repositoryId
 *         - repository
 *         - author
 *         - authorId
 *         - title
 *         - state
 *         - createdAt
 *         - updatedAt
 *         - isDraft
 *         - addedLines
 *         - deletedLines
 *     GitHubIntegration:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         integration:
 *           type: object
 *           $ref: '#/components/schemas/WorkspaceIntegration'
 *         integrationId:
 *           type: string
 *         installationId:
 *           type: integer
 *         accountId:
 *           type: integer
 *         accountLogin:
 *           type: string
 *         accountType:
 *           type: string
 *           $ref: '#/components/schemas/GitHubAccountType'
 *       required:
 *         - id
 *         - updatedAt
 *         - integration
 *         - integrationId
 *         - installationId
 *         - accountId
 *         - accountLogin
 *         - accountType
 *     GitHubWebhook:
 *       type: object
 *       properties:
 *         receivedAt:
 *           type: string
 *           format: date-time
 *         hookId:
 *           type: string
 *           description: X-GitHub-Hook-ID
 *         event:
 *           type: string
 *           description: X-GitHub-Event
 *         delivery:
 *           type: string
 *           description: X-GitHub-Delivery (GUID)
 *         hookInstallationTargetType:
 *           type: string
 *           description: X-GitHub-Hook-Installation-Target-Type
 *         hookInstallationTargetId:
 *           type: string
 *           description: X-GitHub-Hook-Installation-Target-ID
 *         installationId:
 *           type: integer
 *           description: When available, this is the installation ID of the installation that created the webhook (installation.id)
 *         installationAccountId:
 *           type: integer
 *           description: When available, this is the account ID of the installation that created the webhook (installation.account.id)
 *         installationAccountLogin:
 *           type: string
 *           description: When available, this is the login of the account that created the webhook (installation.account.login)
 *         processed:
 *           type: boolean
 *           description: If the webhook was processed successfully
 *         data:
 *           type: object
 *           description: Webhook payload
 *       required:
 *         - receivedAt
 *         - event
 *         - delivery
 *         - processed
 *         - data
 *     WorkspaceIntegration:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         workspaceId:
 *           type: string
 *         channel:
 *           type: string
 *           $ref: '#/components/schemas/IntegrationChannel'
 *         status:
 *           type: string
 *           $ref: '#/components/schemas/IntegrationStatus'
 *         integrationIdOnChannel:
 *           type: string
 *         github:
 *           type: object
 *           $ref: '#/components/schemas/GitHubIntegration'
 *           description: |-
 *             GitHub integration data.
 *             Only available if channel is GitHub.
 *       required:
 *         - id
 *         - createdAt
 *         - updatedAt
 *         - workspace
 *         - workspaceId
 *         - channel
 *         - status
 *         - integrationIdOnChannel
 *     IntegrationState:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         workspaceId:
 *           type: string
 *         channel:
 *           type: string
 *           $ref: '#/components/schemas/IntegrationChannel'
 *       required:
 *         - id
 *         - createdAt
 *         - workspace
 *         - workspaceId
 *         - channel
 *     IntegrationProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         workspaceId:
 *           type: string
 *         channel:
 *           type: string
 *           $ref: '#/components/schemas/IntegrationChannel'
 *         idOnChannel:
 *           type: string
 *           description: |-
 *             The id of this profile in the channel.
 *             May be null if there is not enough information to determine the profile.
 *         username:
 *           type: string
 *         name:
 *           type: string
 *         emails:
 *           type: string
 *         links:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/UserProfileLink'
 *       required:
 *         - id
 *         - workspace
 *         - workspaceId
 *         - channel
 *         - emails
 *         - links
 *     Task:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         channel:
 *           type: string
 *           $ref: '#/components/schemas/IntegrationChannel'
 *         type:
 *           type: string
 *           $ref: '#/components/schemas/TaskType'
 *         workspaceId:
 *           type: string
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         author:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         authorId:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         closedAt:
 *           type: string
 *           format: date-time
 *       required:
 *         - id
 *         - channel
 *         - type
 *         - workspaceId
 *         - workspace
 *         - createdAt
 *     Team:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         parentId:
 *           type: string
 *         parent:
 *           type: object
 *           $ref: '#/components/schemas/Team'
 *         children:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Team'
 *         workspaceId:
 *           type: string
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         members:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/TeamMembership'
 *       required:
 *         - id
 *         - createdAt
 *         - updatedAt
 *         - children
 *         - members
 *     TeamMembership:
 *       type: object
 *       properties:
 *         createdAt:
 *           type: string
 *           format: date-time
 *         teamId:
 *           type: string
 *         team:
 *           type: object
 *           $ref: '#/components/schemas/Team'
 *         userId:
 *           type: string
 *         user:
 *           type: object
 *           $ref: '#/components/schemas/User'
 *       required:
 *         - createdAt
 *         - teamId
 *         - team
 *         - userId
 *         - user
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         email:
 *           type: string
 *         displayName:
 *           type: string
 *         avatar:
 *           type: string
 *         country:
 *           type: string
 *         timezone:
 *           type: string
 *         phone:
 *           type: string
 *         language:
 *           type: string
 *         theme:
 *           type: string
 *         onboarding:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         teams:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/TeamMembership'
 *         workspaces:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceMembership'
 *         invitesPerformed:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceInvite'
 *         invites:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceInvite'
 *         links:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/UserProfileLink'
 *           description: |-
 *             A single user may have multiple profiles on different channels.
 *             For example a single user on BMS Pulse may have two different GitHub accounts.
 *             It will always have at least one profile for the BMS channel.
 *       required:
 *         - id
 *         - language
 *         - theme
 *         - onboarding
 *         - createdAt
 *         - updatedAt
 *         - teams
 *         - workspaces
 *         - invitesPerformed
 *         - invites
 *         - links
 *     UserProfileLink:
 *       type: object
 *       properties:
 *         user:
 *           type: object
 *           $ref: '#/components/schemas/User'
 *         userId:
 *           type: string
 *         profile:
 *           type: object
 *           $ref: '#/components/schemas/IntegrationProfile'
 *         profileId:
 *           type: string
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         workspaceId:
 *           type: string
 *       required:
 *         - user
 *         - userId
 *         - profile
 *         - profileId
 *         - workspace
 *         - workspaceId
 *     Workspace:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: The workspace auto-generated id
 *         name:
 *           type: string
 *           description: |-
 *             The workspace name.
 *             Multiple workspaces may have the same name.
 *         avatar:
 *           type: string
 *           description: The workspace avatar picture
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date when the workspace have been created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last modification of the workspace
 *         roles:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Role'
 *         teams:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Team'
 *         invites:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceInvite'
 *         members:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceMembership'
 *         integrationStates:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/IntegrationState'
 *         integrations:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/WorkspaceIntegration'
 *       required:
 *         - id
 *         - createdAt
 *         - updatedAt
 *         - roles
 *         - teams
 *         - invites
 *         - members
 *         - integrationStates
 *         - integrations
 *     WorkspaceMembership:
 *       type: object
 *       properties:
 *         userId:
 *           type: string
 *           description: The user id
 *         workspaceId:
 *           type: string
 *         roleId:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         role:
 *           type: object
 *           $ref: '#/components/schemas/Role'
 *         user:
 *           type: object
 *           $ref: '#/components/schemas/User'
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *       required:
 *         - userId
 *         - workspaceId
 *         - roleId
 *         - createdAt
 *         - updatedAt
 *         - role
 *         - user
 *         - workspace
 *     WorkspaceInvite:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         email:
 *           type: string
 *         token:
 *           type: string
 *         status:
 *           type: string
 *           $ref: '#/components/schemas/InviteStatus'
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         workspaceId:
 *           type: string
 *         workspace:
 *           type: object
 *           $ref: '#/components/schemas/Workspace'
 *         invitedBy:
 *           type: string
 *         invitedByUser:
 *           type: object
 *           $ref: '#/components/schemas/User'
 *         userId:
 *           type: string
 *         user:
 *           type: object
 *           $ref: '#/components/schemas/User'
 *         roleId:
 *           type: string
 *         role:
 *           type: object
 *           $ref: '#/components/schemas/Role'
 *       required:
 *         - id
 *         - status
 *         - createdAt
 *         - updatedAt
 *         - roleId
 *         - role
 *     Permission:
 *       type: string
 *       enum:
 *         - DELETE_WORKSPACE
 *         - INVITE_MEMBER
 *         - UPDATE_WORKSPACE
 *         - MANAGE_INTEGRATIONS
 *     GitRepositoryVisibility:
 *       type: string
 *       enum:
 *         - Internal
 *         - Private
 *         - Public
 *     GitRepositoryState:
 *       type: string
 *       enum:
 *         - Active
 *         - Archived
 *         - Deleted
 *     GitPullRequestState:
 *       type: string
 *       enum:
 *         - OPEN
 *         - CLOSED
 *         - MERGED
 *     GitHubAccountType:
 *       type: string
 *       enum:
 *         - Enterprise
 *         - Organization
 *         - User
 *     IntegrationStatus:
 *       type: string
 *       enum:
 *         - Active
 *         - Inactive
 *         - Suspended
 *         - Uninstalled
 *         - Synchronizing
 *         - PermissionsOutdated
 *     IntegrationChannel:
 *       type: string
 *       enum:
 *         - GitHub
 *         - GitLab
 *         - Bitbucket
 *         - AzureDevOps
 *         - Slack
 *         - Jira
 *         - Email
 *     TaskType:
 *       type: string
 *       enum:
 *         - Bug
 *         - Feature
 *     InviteStatus:
 *       type: string
 *       enum:
 *         - PENDING
 *         - ACCEPTED
 *         - REJECTED
 */
