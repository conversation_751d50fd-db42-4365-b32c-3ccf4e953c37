import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/invites/{inviteId}/decline:
 *   patch:
 *     summary: Decline workspace invitation
 *     description: Decline a pending workspace invitation. Only the invited user can decline their own invitation.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: inviteId
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation ID
 *     responses:
 *       200:
 *         description: Invitation declined successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invitation declined successfully"
 *       400:
 *         description: Bad request - invitation not found, already processed, or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - user cannot decline this invitation
 *       500:
 *         description: Internal server error
 */
export async function PATCH(request: NextRequest, props: { params: Promise<{ inviteId: string }> }) {
  const params = await props.params;
  try {
    const { inviteId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Get the invitation
    const invitation = await db.workspaceInvite.findUnique({
      where: {
        id: inviteId,
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found' }, { status: 400 });
    }

    // Check if invitation is still pending
    if (invitation.status !== 'PENDING') {
      return NextResponse.json({ error: 'Invitation has already been processed' }, { status: 400 });
    }

    // Check if the current user can decline this invitation
    // They can decline if:
    // 1. The invitation has their userId (they're already in the system), OR
    // 2. The invitation email matches their email (they registered after being invited)
    const canDecline =
      (invitation.userId && invitation.userId === currentUser.uid) ||
      (invitation.email && invitation.email === currentUser.email);

    if (!canDecline) {
      return NextResponse.json({ error: 'You cannot decline this invitation' }, { status: 403 });
    }

    // Update invitation status to declined
    await db.workspaceInvite.update({
      where: { id: inviteId },
      data: {
        status: 'REJECTED',
        userId: currentUser.uid, // Ensure userId is set
      },
    });

    return NextResponse.json({
      message: 'Invitation declined successfully',
    });
  } catch (error) {
    console.error('Error declining workspace invitation:', error);
    return NextResponse.json({ error: 'Failed to decline invitation' }, { status: 500 });
  }
}
