'use client';

import { User } from '@prisma/client';
import React, { createContext, ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react';

import { useApiServices } from '@/hooks/use-api-services';
import { logger } from '@/lib/logger/default-logger';

// Constants
const CACHE_DURATION = 300000; // 5 minutes cache
const STORAGE_KEY = 'user_data_cache';
const TIMESTAMP_KEY = 'user_data_timestamp';

// Function to load cached data from localStorage
const loadCachedData = (): { userData: User | null; timestamp: number } => {
  if (typeof window === 'undefined') {
    return { userData: null, timestamp: 0 };
  }

  try {
    const cachedData = localStorage.getItem(STORAGE_KEY);
    const timestamp = Number(localStorage.getItem(TIMESTAMP_KEY) || '0');

    if (cachedData && timestamp) {
      return {
        userData: JSON.parse(cachedData) as User,
        timestamp,
      };
    }
  } catch (error) {
    console.error('Error loading cached user data:', error);
  }

  return { userData: null, timestamp: 0 };
};

interface UserContextType {
  user: User | null;
  loading: boolean;
  refreshing: boolean;
  error: Error | null;
  fetchUser: (_force?: boolean) => Promise<User | null>;
  clearUserCache: () => Promise<void>;
  updateUser: (_userData: User) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const { userApiService } = useApiServices();

  const initialCache = React.useMemo(() => loadCachedData(), []);

  const [user, setUser] = useState<User | null>(initialCache.userData);
  const [loading, setLoading] = useState(!initialCache.userData);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const saveToCache = useCallback((userData: User, timestamp: number) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(userData));
      localStorage.setItem(TIMESTAMP_KEY, timestamp.toString());
    } catch (err) {
      console.error('Error saving user data to cache:', err);
    }
  }, []);

  // Use a ref to track the fetch promise to avoid duplicate requests
  const fetchPromiseRef = useRef<Promise<User | null> | null>(null);

  const fetchUser = useCallback(
    async (force = false) => {
      // Skip fetching if we have fresh cached data and not forcing refresh
      const now = Date.now();
      const { userData, timestamp } = loadCachedData();

      if (!force && userData && now - timestamp < CACHE_DURATION && !error) {
        setUser(userData);
        setLoading(false);
        return userData;
      }

      // If there's already a fetch in progress, return that promise
      if (fetchPromiseRef.current && !force) {
        return fetchPromiseRef.current;
      }

      // Set loading state and clear any previous errors
      setLoading(true);
      setError(null);
      logger.debug('Clearing previous errors and starting fresh request');

      // Create the fetch promise
      const fetchPromise = (async () => {
        try {
          // Add a small delay to allow multiple components to use the same promise
          await new Promise((resolve) => setTimeout(resolve, 10));

          const currentUser = await userApiService.getCurrentUser();

          // Only update cache if we got a valid user
          if (currentUser) {
            // Update cache in localStorage
            saveToCache(currentUser, now);
            setUser(currentUser);
            logger.debug('User data updated:', { onboarding: currentUser.onboarding });
          } else {
            // If we got null but no error was thrown, create an error
            throw new Error('Failed to fetch user data: User not found');
          }

          return currentUser;
        } catch (err) {
          console.error('Error fetching user data:', err);

          // Keep using cached user data if available
          const { userData: cachedData, timestamp: cacheTimestamp } = loadCachedData();
          if (cachedData) {
            const cacheAge = now - cacheTimestamp;
            logger.debug(`Using cached user data during API error (cache age: ${Math.round(cacheAge / 1000)}s)`);

            // Always keep using the cached data during errors, regardless of age
            setUser(cachedData);

            // Log detailed info about the cache
            logger.debug('Cached user data:', JSON.stringify(cachedData));
          } else {
            logger.debug('No cached user data available during API error');
            setUser(null);
          }

          setError(err instanceof Error ? err : new Error('Failed to fetch user data'));

          return userData || null;
        } finally {
          setLoading(false);
          // Clear the promise reference after a delay
          setTimeout(() => {
            fetchPromiseRef.current = null;
          }, 300);
        }
      })();

      // Store the promise in the ref
      fetchPromiseRef.current = fetchPromise;

      return fetchPromise;
    },
    [userApiService, saveToCache, error]
  );

  // Update user data directly (useful when we get updated data from API calls)
  const updateUser = useCallback(
    (userData: User) => {
      logger.debug('UserContext: Updating user data directly', userData);
      setUser(userData);

      // Update cache with the new data
      const now = Date.now();
      saveToCache(userData, now);
    },
    [saveToCache]
  );

  // Clear the cache (useful for logout)
  const clearUserCache = useCallback(async () => {
    if (typeof window === 'undefined') return;

    logger.debug('UserContext: Clearing user cache and forcing fresh fetch');
    setRefreshing(true);

    localStorage.removeItem(STORAGE_KEY);
    localStorage.removeItem(TIMESTAMP_KEY);
    setUser(null);
    setError(null);

    try {
      // Force fetch fresh user data immediately and wait for it
      await fetchUser(true);
    } finally {
      setRefreshing(false);
    }
  }, [fetchUser]);

  // Fetch user data on mount if not cached or cache is stale
  useEffect(() => {
    const { userData, timestamp } = initialCache;
    const now = Date.now();

    if (!userData || now - timestamp >= CACHE_DURATION || error) {
      fetchUser();
    }
  }, [fetchUser, initialCache, error]);

  // Set up a retry mechanism for errors with exponential backoff
  const [retryAttempt, setRetryAttempt] = useState<number>(0);
  const [nextRetryTime, setNextRetryTime] = useState<number | null>(null);

  // Calculate backoff time based on retry attempts (max 60 seconds)
  const getBackoffTime = useCallback((attempt: number): number => {
    // Start with 5 seconds, then double each time up to 60 seconds
    const seconds = Math.min(5 * Math.pow(2, attempt), 60);
    logger.debug(`Calculated backoff time for attempt ${attempt}: ${seconds} seconds`);
    return seconds * 1000; // Convert to milliseconds
  }, []);

  useEffect(() => {
    if (error && !nextRetryTime) {
      // Calculate backoff time based on current retry attempt
      const backoffTime = getBackoffTime(retryAttempt);

      // Set exact retry time
      const exactRetryTime = Date.now() + backoffTime;
      setNextRetryTime(exactRetryTime);

      logger.debug(`Scheduling context retry... (Attempt ${retryAttempt + 1}, waiting ${backoffTime / 1000}s)`);
      logger.debug(`Next retry at: ${new Date(exactRetryTime).toLocaleTimeString()}`);

      // Schedule the retry with precise timing
      const retryTimer = setTimeout(() => {
        logger.debug(`Executing context retry now (Attempt ${retryAttempt + 1})`);

        // Increment retry attempt for next time
        setRetryAttempt((prev) => prev + 1);

        // Reset next retry time
        setNextRetryTime(null);

        // Force refresh user data
        fetchUser(true);
      }, backoffTime);

      return () => clearTimeout(retryTimer);
    } else if (!error) {
      // Reset retry counter and next retry time when there's no error
      setRetryAttempt(0);
      setNextRetryTime(null);
    }
  }, [error, fetchUser, retryAttempt, getBackoffTime, nextRetryTime]);
  const value = {
    user,
    loading,
    refreshing,
    error,
    fetchUser,
    clearUserCache,
    updateUser,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useCurrentUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useCurrentUser must be used within a UserProvider');
  }
  return context;
}

export function useTimezone() {
  const context = useContext(UserContext);

  if (context === undefined) {
    throw new Error('useTimezone must be used within a UserProvider');
  }

  return context.user?.timezone || 'America/Sao_Paulo';
}
