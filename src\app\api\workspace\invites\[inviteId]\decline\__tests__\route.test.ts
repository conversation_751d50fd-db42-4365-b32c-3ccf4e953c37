/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { PATCH } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/admin', () => ({
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

// Import mocked modules
import { db } from '@/services/db';
import { adminAuth } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const _mockAdminAuth = adminAuth as jest.Mocked<typeof adminAuth>;
const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;

// Helper function to create mock request
function createMockRequest(method: string, token?: string): NextRequest {
  const url = 'http://localhost:3000/api/workspace/invites/invite-1/decline';
  const headers = new Headers();
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  headers.set('Content-Type', 'application/json');

  return new NextRequest(url, {
    method,
    headers,
  });
}

// Mock data
const mockUser = {
  uid: 'user-1',
  email: '<EMAIL>',
  displayName: 'Test User',
};

const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'inviter-1',
  userId: 'user-1',
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
};

const mockDeclinedInvitation = {
  ...mockInvitation,
  status: 'REJECTED',
};

describe('/api/workspace/invites/[inviteId]/decline', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('PATCH', () => {
    it('should decline invitation successfully', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceInvite.update.mockResolvedValue(mockDeclinedInvitation as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation declined successfully');
      expect(mockDb.workspaceInvite.update).toHaveBeenCalledWith({
        where: { id: 'invite-1' },
        data: {
          status: 'REJECTED',
          userId: 'user-1',
        },
      });
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('PATCH');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 404 for non-existent invitation', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(null);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invalid-invite' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for already processed invitation', async () => {
      const acceptedInvitation = { ...mockInvitation, status: 'ACCEPTED' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(acceptedInvitation as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 403 for unauthorized user (different email)', async () => {
      const differentUser = { ...mockUser, uid: 'different-user', email: '<EMAIL>' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: differentUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(403);
    });

    it('should handle database errors', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceInvite.update.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(500);
    });

    it('should decline invitation by userId when email does not match', async () => {
      const invitationWithUserId = { ...mockInvitation, email: '<EMAIL>', userId: 'user-1' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(invitationWithUserId as any);
      mockDb.workspaceInvite.update.mockResolvedValue({ ...invitationWithUserId, status: 'REJECTED' } as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(200);
    });

    it('should return 403 when neither email nor userId matches', async () => {
      const invitationWithDifferentUser = {
        ...mockInvitation,
        email: '<EMAIL>',
        userId: 'different-user',
      };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(invitationWithDifferentUser as any);

      const request = createMockRequest('PATCH', 'valid-token');
      const response = await PATCH(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(403);
    });
  });
});
