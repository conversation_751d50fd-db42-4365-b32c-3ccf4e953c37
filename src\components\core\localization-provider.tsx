'use client';

import { Language } from '@/lib/models/language';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider as Provider } from '@mui/x-date-pickers/LocalizationProvider';
import { useLocale } from 'next-intl';
import * as React from 'react';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import 'dayjs/locale/en';
import 'dayjs/locale/es';
import 'dayjs/locale/pt-br';

dayjs.extend(utc);
dayjs.extend(timezone);

export interface LocalizationProviderProps {
  children: React.ReactNode;
}

export function LocalizationProvider({ children }: LocalizationProviderProps): React.JSX.Element {
  const locale = useLocale();

  let dayJsLocale = 'pt-br';
  switch (locale) {
    case Language.PT_BR:
      dayJsLocale = 'pt-br';
      break;
    case Language.EN_US:
      dayJsLocale = 'en';
      break;
    case Language.ES:
      dayJsLocale = 'es';
      break;
  }

  return <Provider dateAdapter={AdapterDayjs} adapterLocale={dayJsLocale}>{children}</Provider>;
}
