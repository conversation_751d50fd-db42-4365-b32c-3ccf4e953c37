/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { DELETE } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/admin', () => ({
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
    workspaceMembership: {
      findFirst: jest.fn(),
    },
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

// Import mocked modules
import { db } from '@/services/db';
import { adminAuth } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const _mockAdminAuth = adminAuth as jest.Mocked<typeof adminAuth>;
const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;

// Helper function to create mock request
function createMockRequest(method: string, token?: string): NextRequest {
  const url = 'http://localhost:3000/api/workspace/invites/invite-1';
  const headers = new Headers();
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  headers.set('Content-Type', 'application/json');

  return new NextRequest(url, {
    method,
    headers,
  });
}

// Mock data
const mockUser = {
  uid: 'user-1',
  email: '<EMAIL>',
  displayName: 'Test User',
};

const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'user-1',
  userId: null,
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
};

const mockWorkspaceMembership = {
  id: 'membership-1',
  userId: 'user-1',
  workspaceId: 'workspace-1',
  roleId: 'admin',
  role: {
    id: 'admin',
    name: 'Admin',
    permissions: [{ permission: { id: 'INVITE_MEMBER' } }],
  },
};

describe('/api/workspace/invites/[inviteId]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('DELETE', () => {
    it('should delete invitation successfully', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);
      mockDb.workspaceInvite.delete.mockResolvedValue(mockInvitation as any);

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Invitation cancelled successfully');
      expect(mockDb.workspaceInvite.delete).toHaveBeenCalledWith({
        where: { id: 'invite-1' },
      });
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('DELETE');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 400 for non-existent invitation', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(null);

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invalid-invite' }) });

      expect(response.status).toBe(400);
    });

    it('should return 403 for user without permission', async () => {
      const invitationByOtherUser = { ...mockInvitation, invitedBy: 'other-user' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(invitationByOtherUser as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null); // No membership

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(403);
    });

    it('should allow invitation creator to delete their own invitation', async () => {
      const creatorInvitation = { ...mockInvitation, invitedBy: 'user-1' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(creatorInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null); // No special permissions
      mockDb.workspaceInvite.delete.mockResolvedValue(creatorInvitation as any);

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(200);
    });

    it('should return 403 for user who is not creator and has no permissions', async () => {
      const otherUserInvitation = { ...mockInvitation, invitedBy: 'other-user' };
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(otherUserInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(403);
    });

    it('should handle database errors', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(mockInvitation as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);
      mockDb.workspaceInvite.delete.mockRejectedValue(new Error('Database error'));

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(500);
    });

    it('should verify workspace membership permissions correctly', async () => {
      const invitationByOtherUser = { ...mockInvitation, invitedBy: 'other-user' };

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceInvite.findUnique.mockResolvedValue(invitationByOtherUser as any);
      // Mock findFirst to return null (no membership with INVITE_MEMBER permission found)
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);

      const request = createMockRequest('DELETE', 'valid-token');
      const response = await DELETE(request, { params: Promise.resolve({ inviteId: 'invite-1' }) });

      expect(response.status).toBe(403);
    });
  });
});
